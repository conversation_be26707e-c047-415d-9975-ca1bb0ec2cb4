#!/usr/bin/env node

const path = require('path');
const RouteParser = require('./src/runtime-validation/RouteParser');

async function testRouteMapping() {
  console.log('🧪 测试路由映射功能...\n');
  
  const projectPath = '/Users/<USER>/works/galaxy/galaxy-vue3-demi';
  const parser = new RouteParser(projectPath);
  
  try {
    // 解析路由
    const routes = await parser.parseRoutes();
    console.log(`✅ 解析到 ${routes.length} 个路由\n`);
    
    // 获取路由组件映射
    const routeMap = parser.getRouteComponentMap();
    console.log('📋 路由到组件的映射:');
    
    for (const [routePath, componentPath] of routeMap.entries()) {
      console.log(`   ${routePath} → ${componentPath}`);
    }
    
    console.log('\n🔍 测试路径推断功能:');
    
    // 测试几个特定路由的组件路径推断
    const testRoutes = [
      '/charts/keyboard',
      '/charts/line', 
      '/dashboard',
      '/components/avatar-upload'
    ];
    
    for (const routePath of testRoutes) {
      console.log(`\n🎯 路由: ${routePath}`);
      
      // 直接映射
      const directPath = parser.getComponentPathByRoute(routePath);
      if (directPath) {
        console.log(`   直接映射: ${directPath}`);
      }
      
      // 推断路径
      const inferredPaths = parser.inferComponentPaths(routePath, 'KeyboardChart component error');
      console.log(`   推断路径: ${inferredPaths.slice(0, 3).join(', ')}`);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.stack) {
      console.error(error.stack);
    }
  }
}

testRouteMapping();
